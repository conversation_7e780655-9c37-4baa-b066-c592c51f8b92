@use '../../../../styles/shared.scss' as *;

.form-header {
  background-color: white;
  padding: 16px 0;
}

.horizontal-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  padding: 0 24px;
  background-color: white;
}

.form-field {
  display: flex;
  align-items: center;
  
  p {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 18px;
    color: #283A97;
    margin: 0;
    padding: 8px 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    min-width: 120px;
    text-align: center;
  }
}

.button-group {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* Button Container with Rounded Corners */
.button-container {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background-color: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-wrap: nowrap;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

/* Enhanced Main Form Action Button Styles */
.form-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 4px 8px;
  border: 2px solid;
  border-radius: 6px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  white-space: nowrap;

  mat-icon {
    font-size: 10px;
    width: 10px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    margin: 0;
    padding: 0;
  }
}

/* Toggle View Button - Teal theme */
.form-action-button.toggle-view-button {
  border-color: #009688;
  background: linear-gradient(135deg, #009688 0%, #00796B 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #00796B 0%, #00695C 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 150, 136, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 150, 136, 0.3);
  }
}

/* Submit Button - Green theme */
.form-action-button.submit-button {
  border-color: #4CAF50;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
}

/* Validate Button - Blue theme */
.form-action-button.validate-button {
  border-color: #2196F3;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
  }
}

/* Authorize Button - Purple theme */
.form-action-button.authorize-button {
  border-color: #9C27B0;
  background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #7B1FA2 0%, #6A1B9A 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(156, 39, 176, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(156, 39, 176, 0.3);
  }
}

/* Back Button - Gray theme */
.form-action-button.back-button {
  border-color: #607D8B;
  background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #455A64 0%, #37474F 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(96, 125, 139, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(96, 125, 139, 0.3);
  }
}

/* Reject Button - Red theme */
.form-action-button.reject-button {
  border-color: #F44336;
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #D32F2F 0%, #C62828 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
  }
}

/* Delete Button - Dark Red theme */
.form-action-button.delete-button {
  border-color: #D32F2F;
  background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #8E0000 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(211, 47, 47, 0.3);
  }
}

/* Error message styling */
.error-message {
  color: #f44336;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 12px;
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 4px;
  margin-left: 12px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .horizontal-container {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding: 0 16px;
  }
  
  .form-field {
    justify-content: center;
    
    p {
      font-size: 16px;
      min-width: 100px;
    }
  }
  
  .button-group {
    justify-content: center;
    gap: 8px;
  }
  
  .button-container {
    padding: 6px 8px;
    gap: 6px;
    justify-content: center;
  }
  
  .form-action-button {
    padding: 5px 10px;
    font-size: 12px;
    
    mat-icon {
      font-size: 8px;
      width: 8px;
      height: 8px;
    }
  }
}

@media (max-width: 480px) {
  .button-group {
    gap: 6px;
  }
  
  .button-container {
    padding: 4px 6px;
    gap: 4px;
  }
  
  .form-action-button {
    padding: 4px 8px;
    font-size: 11px;
    
    mat-icon {
      font-size: 7px;
      width: 7px;
      height: 7px;
    }
  }
  
  .error-message {
    font-size: 12px;
    padding: 6px 8px;
    margin-left: 8px;
  }
}
.readonly-button {
  pointer-events: none;
  opacity: 0.6;
  cursor: not-allowed;
}